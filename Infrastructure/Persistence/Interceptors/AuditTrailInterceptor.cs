using Domain.System;
using Infrastructure.Persistence.Interceptors.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Infrastructure.Persistence.Interceptors;

/// <summary>
/// Interceptor pro zachycení a zaznamenání změn v entitách do auditního logu
/// </summary>
public class AuditTrailInterceptor : SaveChangesInterceptor
{
    private readonly ILogger<AuditTrailInterceptor> _logger;
    private readonly string _userName;

    /// <summary>
    /// Konstruktor s injektovanými závislostmi
    /// </summary>
    /// <param name="logger">Logger pro záznam případných chyb</param>
    /// <param name="currentUserService">Služba pro získání aktuálního uživatele</param>
    public AuditTrailInterceptor(
        ILogger<AuditTrailInterceptor> logger,
        // TODO: Implementovat a injektovat službu pro aktuálního uživatele
        // ICurrentUserService currentUserService
        string userName = "System")
    {
        _logger = logger;
        _userName = userName; // currentUserService.UserName ?? "System";
    }

    /// <summary>
    /// Metoda volaná před uložením změn v databázi
    /// </summary>
    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result)
    {
        UpdateAuditTrails(eventData.Context);
        return base.SavingChanges(eventData, result);
    }

    /// <summary>
    /// Asynchronní metoda volaná před uložením změn v databázi
    /// </summary>
    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        UpdateAuditTrails(eventData.Context);
        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    /// <summary>
    /// Aktualizuje auditní log na základě změn v entitách
    /// </summary>
    private void UpdateAuditTrails(DbContext? context)
    {
        if (context == null)
            return;

        // Získání všech změněných entit
        foreach (var entry in context.ChangeTracker.Entries())
        {
            // Ignorujeme samotné auditní záznamy a jiné nezajímavé entity
            if (entry.Entity is AuditTrail || entry.State is EntityState.Detached or EntityState.Unchanged)
                continue;

            try
            {
                // Vytvoření a přidání auditního záznamu
                var auditTrail = CreateAuditTrail(entry);
                context.Set<AuditTrail>().Add(auditTrail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Chyba při vytváření auditního záznamu");
            }
        }
    }

    /// <summary>
    /// Vytvoří auditní záznam na základě změn v entitě
    /// </summary>
    private AuditTrail CreateAuditTrail(EntityEntry entry)
    {
        var auditTrail = new AuditTrail
        {
            UserId = _userName,
            TableName = entry.Metadata.GetTableName() ??
                        entry.Entity.GetType()
                            .Name,
            DateTime = DateTime.UtcNow,
            Id = 0
        };

        // Získání a uložení všech originálních hodnot pro aktualizované entity
        if (entry.State == EntityState.Modified)
        {
            var oldValues = new Dictionary<string, object?>();
            var newValues = new Dictionary<string, object?>();
            var affectedColumns = new List<string>();

            foreach (var property in entry.Properties)
            {
                if (property.IsModified || entry.State == EntityState.Added || entry.State == EntityState.Deleted)
                {
                    var propertyName = property.Metadata.Name;
                    
                    affectedColumns.Add(propertyName);
                    
                    if (entry.State != EntityState.Added)
                        oldValues[propertyName] = property.OriginalValue;
                    
                    if (entry.State != EntityState.Deleted)
                        newValues[propertyName] = property.CurrentValue;
                }
            }

            auditTrail.AffectedColumns = affectedColumns;
            auditTrail.OldValues = oldValues;
            auditTrail.NewValues = newValues;
        }

        // Uložení primárního klíče entity
        auditTrail.PrimaryKey = entry.GetPrimaryKeyValue();

        try
        {
            // Uložení kompletního pohledu na entitu pro účely ladění
            auditTrail.DebugView = entry.DebugView.ShortView;
        }
        catch (Exception ex)
        {
            auditTrail.ErrorMessage = ex.Message;
        }

        return auditTrail;
    }
}
