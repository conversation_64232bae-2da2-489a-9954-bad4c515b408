using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace SharedKernel.Extensions;

/// <summary>
/// Statická třída poskytující rozšiřující metody pro konverzi hodnot v EF.
/// Hlavní funkcí je konverze komplexních objektů na JSON řetězce pro uložení v databázi.
/// </summary>
public static class ValueConversionExtensions
{
    /// <summary>
    /// Rozšiřující metoda, která konfiguruje vlastnost entity pro konverzi mezi komplexním objektem a JSON řetězcem.
    /// Umožňuje ukládat komplexní objekty (např. slovníky, seznamy) jako JSON řetězce v databázi.
    /// </summary>
    /// <typeparam name="T">Typ vlastnosti, který bude konvertován na JSON</typeparam>
    /// <param name="propertyBuilder">Builder vlastnosti entity</param>
    /// <returns>Builder vlastnosti entity pro podporu řetězového volání metod</returns>
    public static PropertyBuilder<T?> HasJsonConversion<T>(this PropertyBuilder<T?> propertyBuilder)
    {
        // Získání standardních možností pro serializaci JSON
        var options = DefaultJsonSerializerOptions.Options;

        // Vytvoření konvertoru, který převádí mezi typem T a řetězcem JSON
        var converter = new ValueConverter<T?, string>(
            // Konverze z T na string (serializace)
            v => JsonSerializer.Serialize(v, options),
            // Konverze ze string na T (deserializace) s ošetřením null hodnot
            v => string.IsNullOrEmpty(v) ? default : JsonSerializer.Deserialize<T>(v, options));

        // Vytvoření porovnávače pro správné sledování změn v ChangeTrackeru
        // Bez tohoto by EF nepoznal změny v komplexních objektech
        var comparer = new ValueComparer<T?>(
            // Porovnání dvou hodnot - serializujeme obě a porovnáme JSON řetězce
            (l, r) => JsonSerializer.Serialize(l, options) == JsonSerializer.Serialize(r, options),
            // Výpočet hash kódu na základě serializovaného JSON
            v => v == null ? 0 : JsonSerializer.Serialize(v, options).GetHashCode(),
            // Vytvoření kopie objektu pomocí deserializace serializovaného JSON
            v => JsonSerializer.Deserialize<T>(JsonSerializer.Serialize(v, options), options));

        // Nastavení konvertoru pro vlastnost
        propertyBuilder.HasConversion(converter);
        // Nastavení porovnávače pro správné sledování změn v ChangeTrackeru
        propertyBuilder.Metadata.SetValueComparer(comparer);

        return propertyBuilder;
    }
}

/// <summary>
/// Statická třída poskytující standardní možnosti pro JSON serializaci.
/// Tyto možnosti jsou optimalizované pro použití v databázových konverzích.
/// </summary>
public static class DefaultJsonSerializerOptions
{
    /// <summary>
    /// Standardní možnosti pro JSON serializaci s optimálním nastavením pro databázové operace.
    /// </summary>
    public static readonly JsonSerializerOptions Options = new()
    {
        // Povolení čtení komentářů v JSON (pro flexibilitu)
        ReadCommentHandling = JsonCommentHandling.Skip,
        // Povolení koncových čárek v JSON objektech a polích
        AllowTrailingCommas = true,
        // Nastavení enkodéru pro správné zobrazení Unicode znaků
        Encoder = JavaScriptEncoder.Create(UnicodeRanges.BasicLatin, UnicodeRanges.CjkUnifiedIdeographs),
        // Použití camelCase pro názvy vlastností (např. FirstName -> firstName)
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        // Ignorování velikosti písmen při deserializaci
        PropertyNameCaseInsensitive = true,
        // Přidání konvertoru pro výčtové typy, aby byly serializovány jako řetězce
        Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
    };
}
