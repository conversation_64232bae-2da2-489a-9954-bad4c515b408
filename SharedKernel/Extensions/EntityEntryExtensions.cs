using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace SharedKernel.Extensions;

/// <summary>
/// Rozšiřující metody pro práci s EntityEntry v kontextu auditování.
/// Tyto metody poskytují obecné funkcionality pro práci s entitami v Entity Framework.
/// </summary>
public static class EntityEntryExtensions
{
    /// <summary>
    /// Získá hodnotu primárního klíče pro danou entitu.
    /// </summary>
    /// <param name="entry">Entry reprezentující entitu</param>
    /// <returns>Slovník s hodnotami primárního klíče</returns>
    public static Dictionary<string, object> GetPrimaryKeyValue(this EntityEntry entry)
    {
        var result = new Dictionary<string, object>();
        var keyNames = entry.Metadata.FindPrimaryKey()?.Properties.Select(p => p.Name);

        if (keyNames == null) 
            return result;

        foreach (var keyName in keyNames)
        {
            var property = entry.Property(keyName);
            if (property.CurrentValue != null)
            {
                result[keyName] = property.CurrentValue;
            }
        }

        return result;
    }

    /// <summary>
    /// Získá názvy všech změněných vlastností entity.
    /// </summary>
    /// <param name="entry">Entry reprezentující entitu</param>
    /// <returns>Seznam názvů změněných vlastností</returns>
    public static List<string> GetModifiedPropertyNames(this EntityEntry entry)
    {
        return entry.Properties
            .Where(p => p.IsModified)
            .Select(p => p.Metadata.Name)
            .ToList();
    }

    /// <summary>
    /// Získá původní hodnoty všech vlastností entity.
    /// </summary>
    /// <param name="entry">Entry reprezentující entitu</param>
    /// <returns>Slovník s původními hodnotami vlastností</returns>
    public static Dictionary<string, object?> GetOriginalValues(this EntityEntry entry)
    {
        var result = new Dictionary<string, object?>();
        
        foreach (var property in entry.Properties)
        {
            result[property.Metadata.Name] = property.OriginalValue;
        }

        return result;
    }

    /// <summary>
    /// Získá aktuální hodnoty všech vlastností entity.
    /// </summary>
    /// <param name="entry">Entry reprezentující entitu</param>
    /// <returns>Slovník s aktuálními hodnotami vlastností</returns>
    public static Dictionary<string, object?> GetCurrentValues(this EntityEntry entry)
    {
        var result = new Dictionary<string, object?>();
        
        foreach (var property in entry.Properties)
        {
            result[property.Metadata.Name] = property.CurrentValue;
        }

        return result;
    }
}
