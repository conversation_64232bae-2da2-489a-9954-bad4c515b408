# SharedKernel

Tento projekt obsahuje obecné sdílené funkcionality, které mohou být použity napříč všemi vrstvami aplikace.

## Účel projektu

SharedKernel slouží jako centrální místo pro:

1. **Obecné modely** - Result pattern, str<PERSON><PERSON><PERSON><PERSON>, utility třídy
2. **Extension metody** - Rozšiřující metody pro běžné operace
3. **Konstanty a výčty** - Sdílené konstanty a enumerace
4. **Utility funkce** - Pomocné metody pro práci s daty

## Architektonické zásady

SharedKernel je navržen podle následujících principů:

- **Nezávislost** - Nemá závislosti na jiných vrstvách aplikace
- **Obecnost** - Obsahuje pouze obecné funkcionality použitelné napříč projekty
- **Stabilita** - Změny v SharedKernel by m<PERSON><PERSON> být minimální a zpětně kompatibilní

## Struktura projektu

```
SharedKernel/
├── Models/          # Obecné modely (Result, PagedList, atd.)
├── Extensions/      # Extension metody
├── Constants/       # Konstanty a výčty
└── Utilities/       # Utility třídy a metody
```

## Použití

SharedKernel může být referencován ze všech vrstev aplikace:
- Domain
- Application  
- Infrastructure
- Presentation (DataCapture)

## Závislosti

SharedKernel má minimální závislosti pouze na:
- .NET 9.0 runtime
- Entity Framework Core (pro extension metody)
- System.Text.Json (pro JSON konverze)
