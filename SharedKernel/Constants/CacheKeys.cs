namespace SharedKernel.Constants;

/// <summary>
/// Konstanty pro klíče cache používané napříč aplikací.
/// Centralizace klíčů umožňuje snadnou správu a předchází duplicitám.
/// </summary>
public static class CacheKeys
{
    /// <summary>
    /// Prefix pro všechny cache klíče aplikace.
    /// </summary>
    public const string AppPrefix = "DataCapture";

    /// <summary>
    /// Separator používaný v cache klíčích.
    /// </summary>
    public const string Separator = "_";

    /// <summary>
    /// Vytvoří cache klíč s aplikačním prefixem.
    /// </summary>
    /// <param name="key">Základní klí<PERSON></param>
    /// <returns>Kompletní cache klíč s prefixem</returns>
    public static string WithPrefix(string key) => $"{AppPrefix}{Separator}{key}";

    /// <summary>
    /// Vytvoří cache klíč pro entitu.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="operation">Typ operace (GetAll, GetPaged, atd.)</param>
    /// <returns>Cache klíč pro entitu</returns>
    public static string ForEntity(string entityName, string operation) 
        => WithPrefix($"{operation}{Separator}{entityName}");

    /// <summary>
    /// Vytvoří cache klíč pro stránkovaný dotaz.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="sortBy">Řazení podle</param>
    /// <param name="sortDescending">Sestupné řazení</param>
    /// <returns>Cache klíč pro stránkovaný dotaz</returns>
    public static string ForPagedQuery(string entityName, int pageNumber, int pageSize, 
        string? sortBy = null, bool sortDescending = false)
    {
        var key = $"GetPaged{Separator}{entityName}{Separator}Page{pageNumber}{Separator}Size{pageSize}";
        
        if (!string.IsNullOrEmpty(sortBy))
        {
            key += $"{Separator}Sort{sortBy}";
            if (sortDescending)
                key += "Desc";
        }
        
        return WithPrefix(key);
    }

    /// <summary>
    /// Vytvoří cache klíč pro uživatelská data.
    /// </summary>
    /// <param name="userId">ID uživatele</param>
    /// <param name="dataType">Typ dat</param>
    /// <returns>Cache klíč pro uživatelská data</returns>
    public static string ForUser(string userId, string dataType) 
        => WithPrefix($"User{Separator}{userId}{Separator}{dataType}");
}
