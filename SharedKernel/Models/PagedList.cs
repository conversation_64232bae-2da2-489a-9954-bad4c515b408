using Microsoft.EntityFrameworkCore;

namespace SharedKernel.Models;

/// <summary>
/// Obecná třída pro stránkování seznamů dat.
/// Poskytuje informace o aktuální stránce, celkovém počtu položek a navigaci.
/// </summary>
/// <typeparam name="T">Typ položek v seznamu</typeparam>
public class PagedList<T>
{
    /// <summary>
    /// Seznam položek na aktuální stránce.
    /// </summary>
    public List<T> Items { get; }
    
    /// <summary>
    /// Číslo aktuální stránky (začíná od 1).
    /// </summary>
    public int PageNumber { get; }
    
    /// <summary>
    /// Celkový počet stránek.
    /// </summary>
    public int TotalPages { get; }
    
    /// <summary>
    /// Celkový počet položek napříč všemi stránkami.
    /// </summary>
    public int TotalCount { get; }
    
    /// <summary>
    /// Velikost stránky (počet položek na stránku).
    /// </summary>
    public int PageSize { get; }
    
    /// <summary>
    /// Indikuje, zda existuje předchozí stránka.
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;
    
    /// <summary>
    /// Indikuje, zda existuje následující stránka.
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// Konstruktor pro vytvoření stránkovaného seznamu.
    /// </summary>
    /// <param name="items">Seznam položek na aktuální stránce</param>
    /// <param name="count">Celkový počet položek</param>
    /// <param name="pageNumber">Číslo aktuální stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    public PagedList(List<T> items, int count, int pageNumber, int pageSize)
    {
        PageNumber = pageNumber;
        TotalPages = (int)Math.Ceiling(count / (double)pageSize);
        TotalCount = count;
        PageSize = pageSize;
        Items = items;
    }

    /// <summary>
    /// Asynchronně vytvoří stránkovaný seznam z IQueryable zdroje s mapováním.
    /// </summary>
    /// <typeparam name="TSource">Typ zdrojových dat</typeparam>
    /// <param name="source">IQueryable zdroj dat</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="map">Funkce pro mapování ze zdrojového typu na cílový typ</param>
    /// <param name="cancellationToken">Token pro zrušení operace</param>
    /// <returns>Stránkovaný seznam</returns>
    public static async Task<PagedList<T>> CreateAsync<TSource>(
        IQueryable<TSource> source, 
        int pageNumber, 
        int pageSize, 
        Func<TSource, T> map,
        CancellationToken cancellationToken = default)
    {
        var count = await source.CountAsync(cancellationToken);
        
        var items = await source
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        var mappedItems = items.Select(map).ToList();
        
        return new PagedList<T>(mappedItems, count, pageNumber, pageSize);
    }
}
