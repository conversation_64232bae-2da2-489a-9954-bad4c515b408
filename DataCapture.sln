
Microsoft Visual Studio Solution File, Format Version 12.00
#
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataCapture", "DataCapture\DataCapture.csproj", "{02C2CFEA-034C-46A3-B9FA-ECA2E6BC98CE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Domain", "Domain\Domain.csproj", "{2011EF19-4FA3-4BC9-B22A-3771C0967CE9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Application", "Application\Application.csproj", "{B14DD3A4-0E38-42A4-9EA7-0C0447683185}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure", "Infrastructure\Infrastructure.csproj", "{DF2DE8A3-3424-4AED-A64E-218E1D3A2348}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure.Tests", "Infrastructure.Tests\Infrastructure.Tests.csproj", "{F1FBA8D7-348E-46D5-9B37-F1A1463A5629}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Architecture.Tests", "Architecture.Tests\Architecture.Tests.csproj", "{0357C43F-4A26-4531-A2B7-E78B8854AF3F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SharedKernel", "SharedKernel\SharedKernel.csproj", "{C1CDBB3A-23A8-436A-97D1-E4CB2F076CA0}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{02C2CFEA-034C-46A3-B9FA-ECA2E6BC98CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02C2CFEA-034C-46A3-B9FA-ECA2E6BC98CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02C2CFEA-034C-46A3-B9FA-ECA2E6BC98CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02C2CFEA-034C-46A3-B9FA-ECA2E6BC98CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{2011EF19-4FA3-4BC9-B22A-3771C0967CE9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2011EF19-4FA3-4BC9-B22A-3771C0967CE9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2011EF19-4FA3-4BC9-B22A-3771C0967CE9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2011EF19-4FA3-4BC9-B22A-3771C0967CE9}.Release|Any CPU.Build.0 = Release|Any CPU
		{B14DD3A4-0E38-42A4-9EA7-0C0447683185}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B14DD3A4-0E38-42A4-9EA7-0C0447683185}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B14DD3A4-0E38-42A4-9EA7-0C0447683185}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B14DD3A4-0E38-42A4-9EA7-0C0447683185}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF2DE8A3-3424-4AED-A64E-218E1D3A2348}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF2DE8A3-3424-4AED-A64E-218E1D3A2348}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF2DE8A3-3424-4AED-A64E-218E1D3A2348}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF2DE8A3-3424-4AED-A64E-218E1D3A2348}.Release|Any CPU.Build.0 = Release|Any CPU
		{F1FBA8D7-348E-46D5-9B37-F1A1463A5629}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F1FBA8D7-348E-46D5-9B37-F1A1463A5629}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F1FBA8D7-348E-46D5-9B37-F1A1463A5629}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F1FBA8D7-348E-46D5-9B37-F1A1463A5629}.Release|Any CPU.Build.0 = Release|Any CPU
		{0357C43F-4A26-4531-A2B7-E78B8854AF3F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0357C43F-4A26-4531-A2B7-E78B8854AF3F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0357C43F-4A26-4531-A2B7-E78B8854AF3F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0357C43F-4A26-4531-A2B7-E78B8854AF3F}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1CDBB3A-23A8-436A-97D1-E4CB2F076CA0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1CDBB3A-23A8-436A-97D1-E4CB2F076CA0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1CDBB3A-23A8-436A-97D1-E4CB2F076CA0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1CDBB3A-23A8-436A-97D1-E4CB2F076CA0}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
